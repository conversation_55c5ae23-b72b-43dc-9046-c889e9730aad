---
description: 项目规则说明
globs:
alwaysApply: false
---
# Cursor 规则文档索引

本目录包含了 Vue3 项目的完整开发规则和指导文档，帮助 AI 更好地理解和维护项目代码。

## 规则文件概览

### 📋 [项目基础规则 (project.mdc)](mdc:project.mdc)
- 项目技术栈介绍
- 基础开发规范
- 文件命名规范
- 导入顺序规范
- 代码质量要求

### 🏗️ [项目架构规则 (architecture.mdc)](mdc:architecture.mdc)
- 目录结构说明
- 关键文件介绍
- 组件分类规范
- 路由架构设计
- 状态管理架构
- API 接口架构

### 🧩 [组件开发规则 (components.mdc)](mdc:components.mdc)
- 组件开发原则
- 组件分类与规范
- 现有组件介绍
- 组件开发规范
- 样式规范
- 组件通信规范
- 组件文档规范

### 🔌 [API 接口规则 (api-patterns.mdc)](mdc:api-patterns.mdc)
- API 架构概述
- 接口开发规范
- 类型定义规范
- 请求方法规范
- Mock 数据规范
- 组件中 API 使用模式
- 最佳实践

### 🎨 [样式开发规则 (styling.mdc)](mdc:styling.mdc)
- 样式架构概述
- UnoCSS 使用规范
- 组件样式规范
- Ant Design Vue 样式定制
- 动画和过渡
- 暗色主题支持
- 性能优化
- 最佳实践

### 🔧 [开发规范与流程 (development.mdc)](mdc:development.mdc)
- 开发环境要求
- 开发流程
- 代码质量要求
- 文件组织规范
- 性能优化
- 测试规范
- 错误处理
- 安全规范
- 部署规范
- 文档规范
- 团队协作

## 快速导航

### 新手入门
1. 阅读 [项目基础规则](mdc:project.mdc) 了解项目概况
2. 查看 [项目架构规则](mdc:architecture.mdc) 理解项目结构
3. 学习 [开发规范与流程](mdc:development.mdc) 掌握开发流程

### 组件开发
1. 参考 [组件开发规则](mdc:components.mdc) 了解组件规范
2. 查看 [样式开发规则](mdc:styling.mdc) 学习样式写法
3. 使用现有公共组件，避免重复造轮子

### API 开发
1. 遵循 [API 接口规则](mdc:api-patterns.mdc) 进行接口开发
2. 使用统一的请求封装和错误处理
3. 定义清晰的类型接口

### 样式开发
1. 优先使用 UnoCSS 原子类
2. 参考 [样式开发规则](mdc:styling.mdc) 定义 shortcuts
3. 保持样式的一致性和可维护性

## 项目特色

### 技术栈
- **Vue 3.5.17** + **TypeScript** - 现代化前端框架
- **Ant Design Vue 4.2.6** - 企业级 UI 组件库
- **UnoCSS** - 原子化 CSS 框架
- **Pinia** - 状态管理
- **Vite** - 快速构建工具

### 开发理念
- **组件化开发** - 单一职责，高度复用
- **类型安全** - 严格的 TypeScript 类型检查
- **原子化样式** - UnoCSS 提供灵活的样式方案
- **规范化开发** - 统一的代码规范和开发流程

### 最佳实践
- 优先使用已安装的依赖和封装的组件
- 遵循单一职责原则，保持代码整洁
- 使用 TypeScript 提供类型安全
- 采用原子化 CSS 提高样式复用性
- 统一的 API 接口管理和错误处理

## 注意事项

1. **严格遵循"不要求不修改"原则** - 没有明确要求时，不要随意改动现有功能
2. **优先使用现有组件** - 查看 [src/components/](mdc:src/components) 目录中的公共组件
3. **保持代码整洁** - 及时清理废弃代码，添加必要注释
4. **类型安全** - 避免使用 `any` 类型，为所有数据定义明确类型
5. **样式一致性** - 优先使用 UnoCSS 原子类，保持视觉统一

## 更新日志

- 初始版本：创建完整的 Cursor 规则文档体系
- 包含项目基础、架构、组件、API、样式、开发流程等全方位规则

---

*这些规则文档将帮助 AI 更好地理解项目结构和开发规范，提供更准确和一致的代码建议。*
