{"name": "antd-vue3", "version": "0.3.1", "author": {"name": "BWrong", "email": "<EMAIL>", "url": "https://github.com/BWrong"}, "description": "", "keywords": [], "private": true, "type": "module", "scripts": {"dev": "vite --host", "serve": "vite --host", "serve:mock": "vite --mode mock --host", "build": "vite build", "build:check": "npm run type-check && npm run build", "preview": "vite preview", "type-check": "vue-tsc --build --force", "lint": "eslint ./src --fix", "format": "prettier --write src/", "release": "release-it", "changelog": "release-it --changelog", "postinstall": "simple-git-hooks"}, "dependencies": {"@bprogress/core": "^1.3.4", "@bwrong/auth-tool": "^3.0.11", "@bwrong/request": "^1.0.1", "@bwrong/storage": "^1.1.0", "ant-design-vue": "^4.2.6", "axios": "^1.10.0", "crypto-js": "^4.2.0", "css-vars-ponyfill": "^2.4.9", "dayjs": "^1.11.13", "js-cookie": "^3.0.5", "pinia": "^3.0.3", "qs": "^6.14.0", "vue": "^3.5.17", "vue-hooks-plus": "^2.4.0", "vue-router": "^4.5.1"}, "devDependencies": {"@eslint/js": "^9.31.0", "@faker-js/faker": "^9.9.0", "@plugin-web-update-notification/vite": "^2.0.0", "@release-it/conventional-changelog": "^10.0.1", "@tsconfig/node-lts": "18.12.2", "@types/crypto-js": "^4.2.2", "@types/js-cookie": "^3.0.6", "@types/node": "^24.0.15", "@unocss/eslint-config": "^66.3.3", "@unocss/preset-rem-to-px": "^66.3.3", "@unocss/reset": "^66.3.3", "@vitejs/plugin-vue": "^6.0.0", "@vitejs/plugin-vue-jsx": "^5.0.1", "@vue-hooks-plus/resolvers": "^1.3.0", "@vue/eslint-config-prettier": "^10.2.0", "@vue/eslint-config-typescript": "^14.6.0", "@vue/tsconfig": "^0.7.0", "autoprefixer": "^10.4.21", "eslint": "^9.31.0", "eslint-plugin-simple-import-sort": "^12.1.1", "eslint-plugin-vue": "^10.3.0", "less": "^4.4.0", "lint-staged": "^16.1.2", "postcss": "^8.5.6", "postcss-px-to-viewport": "^1.1.1", "prettier": "^3.6.2", "release-it": "^19.0.4", "rollup-plugin-visualizer": "^6.0.3", "simple-git-hooks": "^2.13.0", "typescript": "^5.8.3", "unocss": "^66.3.3", "unplugin-auto-import": "^19.3.0", "unplugin-vue-components": "^28.8.0", "vite": "^7.0.5", "vite-plugin-build-info": "^0.1.2", "vite-plugin-compression2": "^2.2.0", "vite-plugin-env-parse": "^1.0.15", "vite-plugin-html": "^3.2.2", "vite-plugin-iconfont": "^1.5.3", "vite-plugin-mock-dev-server": "^1.9.1", "vue-tsc": "^3.0.3"}, "pnpm": {"peerDependencyRules": {"ignoreMissing": ["rollup"]}, "ignoredBuiltDependencies": ["simple-git-hooks"], "onlyBuiltDependencies": ["core-js", "esbuild", "vue-demi"]}, "engines": {"node": ">=20.0.0", "pnpm": ">=10.0.0"}, "simple-git-hooks": {"pre-commit": "npx lint-staged --allow-empty", "preserveUnused": true}, "lint-staged": {"src/*.{vue,js,jsx,cjs,mjs,ts,tsx}": ["prettier --write", "eslint --fix"], "src/*.{css,less}": ["prettier --write"]}, "packageManager": "pnpm@10.13.1"}