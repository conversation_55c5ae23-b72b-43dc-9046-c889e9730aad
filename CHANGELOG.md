# 更新日志 🎉🎉🎉

## [0.3.1](https://code.srdcloud.cn:29418/zion-governance/template-vue3/compare/0.3.0...0.3.1) (2025-01-06)

### 🐛Bug修复

* 修复类型错误 ([6f56951](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/6f56951078a31fb801d710946d23443adb7b4713))

### 📖文档变更

* readme添加changelog链接 ([2ad2930](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/2ad2930fdfb58b903c9592367e798e2f24e9c283))

### 🔥依赖构建调整

* 升级依赖 ([e8fc5a2](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/e8fc5a2c513ff95adf5aa3b51c538861ef3a98cb))
* 升级依赖 ([0291f66](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/0291f66a813f2330e5d26009ca763a831ee77891))
* 依赖升级 ([0fdf3dc](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/0fdf3dce2329bbc78f6fb01d667a90c1fd4dd3ed))
* 依赖升级 ([32b98b0](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/32b98b00862dc0bbd9c9da7c608d918237701cf8))

### 🚧其他

* 🎨 升级依赖 ([77cfb24](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/77cfb24c703ffa903c8e9bc995cb43f9d8a2272a))

## 0.3.0 (2024-10-12)

### ✨新功能

* 初始化 ([7dcdb99](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/7dcdb999ad944d51fa436278421c7e0f43d0f0e5))
* 添加 devtools ([119a762](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/119a7622508102a54ea1d7ebb8ad82baac1de507))
* 添加多标签页功能 ([cf6b587](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/cf6b587847deeed3f74cbc605b0d5d32715ad21d))
* 添加全局变量注入功能 ([5686af4](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/5686af427f07c5920dd24c950b143fa3a3d5c118))
* 添加tab ([a5df938](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/a5df93863cb2d0c874ed0111ab42366bf13f24b5))
* 新增环境变量解析 ([2901000](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/290100060bec63ed2ec6210844c30ceb9a621e1d))
* 增加布局方式 ([cdc6cfc](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/cdc6cfca3149659256bd5286180e6367d46f6dca))
* 增加外链支持 ([ceeb5b2](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/ceeb5b25caabc11ecaee3cd956c8d177d489f132))

### 🐛Bug修复

* 处理图标注入问题 ([d175b3f](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/d175b3fdfe422ecd8089f8c0d33fa855f270f146))
* 修复弹窗上下文丢失问题 ([96746e7](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/96746e7f4d4373b8cb66e212416a208808bdf708))
* 优化弹窗类型推断 ([85ac1f1](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/85ac1f15562635bd7f3beb1ed91e3f1d5f901e68))
* 优化弹窗hooks和组件 ([b19750a](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/b19750aa6286efcae7b0aa77623fc6d99a2a3060))
* 优化删除按钮 ([e811054](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/e81105478c87b6b2ca3b4c17217098fde8c57f62))
* 优化删除按钮 ([2725942](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/2725942d83e8b345528b9843bfea1e457a0b1b27))

### 🔥依赖构建调整

* 🎨 修改构建配置 ([3d65403](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/3d6540316d7b3387594f69dfaa65e21aa3e629dd))
* 调整工程配置 ([036f6ad](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/036f6ad08372167b3a9cb9387528e2f2d913dea2))
* 更新eslint配置 ([6d08cef](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/6d08cef249a6622d507322d9ba04c67b180db426))
* 去掉调试工具 ([63b0346](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/63b0346d674aaa2f8a33d946309ac276d2142c82))
* 升级 eslintV9 及配置 ([7d26e66](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/7d26e6651b20e7fb95526e31906b43fe251dc3c9))
* 升级依赖 ([25f8d9b](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/25f8d9b4bdc7e074058abe6785a854284c03c0db))
* 升级依赖 ([78380d0](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/78380d0b89d58015afa2499aa01a3f79163d22b0))
* 升级依赖 ([63d6e44](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/63d6e4406f719a809c7da8a5ce802980dfb30f6d))
* 升级依赖 ([d78b976](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/d78b976ec27ecc6a7d0842242817bc0f85a77080))
* 升级依赖 ([df333ff](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/df333ffc6e3ffc9510830541fbc760ce278902c5))
* 升级依赖 ([3deffbe](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/3deffbefd2a136222b2b1c01024865138fd401f5))
* 升级依赖包版本 ([c33ed54](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/c33ed54ed2c4575a565d44eaa7f70db962e785dc))
* 依赖更新 ([6741805](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/6741805b426a0dd875a998594879c98c91a2326e))
* 依赖升级 ([c5b0ba6](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/c5b0ba69dfcf9767671268b0f7329bcd58ce3df0))

### 🚧其他

* 调整lint配置 ([fff3f2a](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/fff3f2a720ed1e3455c1679864a3df8b196918e9))
* 更新依赖 ([b35e50c](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/b35e50cc2744816ad54add08675b432f599a56f1))
* 更新eslint配置 ([9da8040](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/9da8040f80e6d7d27627c17591584831b885594c))
* 更新eslint配置 ([25c039f](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/25c039fdd94dfa7dd85a69ebeb0edd5530f88066))
* 升级依赖 ([466790b](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/466790b3328e3359796c06d0c4887f47d58e9b7b))
* 升级依赖 ([ba646f2](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/ba646f24448141c3ee307108f742d645841ddace))
* 升级依赖 ([0457dc8](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/0457dc8a4f2bed2e77ce593eede735e0da887683))
* 升级依赖 ([a943a80](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/a943a80caf0d481605f84790cab750fc15a2ab71))
* 修复组件提示丢失问题 ([a081c9e](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/a081c9e8166c66d2d85388e01c7afc61aa952c83))
* 依赖升级 ([7430739](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/7430739dcf1a933aa895cfaa55e14042d746136f))
* 优化弹窗上下文 ([d6d4f61](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/d6d4f61996e58265152d12063250dd652db804cb))
* 优化配置 ([f91fb2c](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/f91fb2c5554d6e34310d0568fafa9521dc23df54))
* 优化请求hooks ([f9ec2ba](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/f9ec2baa21d8899ad1dde4789a82e2459f338b29))
* 主题修改 ([c6f5ace](https://code.srdcloud.cn:29418/zion-governance/template-vue3/commit/c6f5ace041cad362c246da0eae2866e2820780b2))

## [0.2.0](http://10.18.101.198:30088/zdww/template-vue3-pc/compare/v0.1.3...v0.2.0) (2023-11-13)


### ✨新功能

* ✨ 新增网站更新提示 ([cca42db](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/cca42dbe81b2ec1676e09a69e136e4a0c996fd83))
* ✨ 增加运行时配置 ([4e87fe1](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4e87fe1fce4d9f10da84b79e2b9d74b1cdc2276d))
* 弹窗增加loading ([609af35](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/609af3572f51162edf0523598f2f8a0c5f1c2e3b))
* 升级，见更新文档 ([4f1fac6](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4f1fac62295e77a9df39011ce566b073b8ace7b7))
* 优化弹窗组件，支持应用上下文 ([3e1e64a](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/3e1e64a48285835e52894f137d6a99a66f83b28b))
* 增加更多主题设置项 ([42c239c](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/42c239cf51fccc2b575d0fa9da42ff68fc6ebde7))
* 增加主题 ([e7266de](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/e7266de024e2bceb7939939e88698261aae27cb4))
* 增加主题配置 ([109990b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/109990bbaed262ba842bc30da649baab938f2f10))
* 增加主题切换 ([deb4f1c](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/deb4f1c55cbccb78c05a8e872318f74dfcfa9c39))
* 主题配置增加持久化 ([23c2984](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/23c29844c2c9ddf285427ec4b94af90c90ef1234))


### 🐛Bug修复

* 🎨 修复权限设置问题 ([ef40dd6](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/ef40dd6bd4b035ac555ca63fd4d309a424bfa7ec))
* 🎨 修复组件类型获取不到问题 ([6949f78](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/6949f78cc732609398b22c43119f781257bc803a))
* 代码调整 ([0c33e07](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/0c33e0720621507ba8a715bc1d7813395c6807f8))
* 代码调整 ([b611f03](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/b611f039b1436df1b24443abaa1469c4c347f433))
* 弹窗增加插槽案例 ([39c2862](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/39c28622a7d56a98111f9a4e3867bff58991032e))
* 修复报错 ([a005018](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/a0050182fec7ca8875251f9720fe351d9c3a70fa))
* 修复弹窗默认值 ([77441e0](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/77441e0b8e37da993d2f272727c108f64a916495))
* 修复axios类型错误 ([f846cf0](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/f846cf0b4f2e564e0f7659fda397c5424c9ccc74))
* 修复build时css报错问题 ([637293f](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/637293fd4e2057828bd5986e94449f9b82e15557))
* 修复table组件props错误 ([5fb8382](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/5fb8382ee4ac608337d0a2517853a721baa425db))
* 修复welink环境下载图标失败问题 ([baf5091](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/baf5091cc373375db6167fd9be4e02f0969824e0))
* 一些代码优化 ([4e73691](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4e73691e25c6341b120d33c4793a94df09d63e81))
* 优化表格默认值 ([d655f08](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d655f08405963dfd25fed661c7c3c7442fbd3f34))
* 优化弹窗类型 ([892ac59](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/892ac59b1782c47fcedd9ada872f46e0b5ee22c6))
* 优化弹窗组件 ([d3c56ee](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d3c56ee690e3f1a6650f38d85052ef80d4e0236f))
* 优化弹窗组件 ([d08b879](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d08b87962cc74396732ed411fa679cfdf4e101fe))
* 优化图标使用方式 ([9316819](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/9316819cf7b0d3104043d6b29153e769bb694bd6))
* 优化主题配置 ([d13de59](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d13de593c78371f963cab9749e3802b4650c1fd1))
* 优化主题配置 ([39f0ea8](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/39f0ea8a2dc11b00715060b45cd78b6af6ff3524))
* 优化eslint忽略文件 ([4bdf69c](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4bdf69cbac2d38c1673caf0988b5903c9dd5b5b7))
* mock环境代理问题处理 ([760f738](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/760f738e27f43f4a8cddd9c1a20e3a5b6a9615ca))


### 📖文档变更

* 更新弹窗组件文档 ([fe97411](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/fe97411973e9ddfbf2287120371aec8542ef47d0))
* 修改路由文档 ([822bad5](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/822bad5df4c3c6298ee8e943f5fe5c908d7a52db))


### 🗃️代码重构

* 调整结构 ([4087a4b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4087a4beb60f4dab4c88ab8665a0c142b6270afa))


### 🔥依赖构建调整

* 代码优化 ([c47ddc4](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/c47ddc4fd3d3b5bcac431905b64c64293c82982d))
* 调整代理 ([d653e89](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d653e89718c27f6927bf31dc76346391c0a2eb19))
* 调整依赖 ([c334ac2](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/c334ac228c4b40e679946c1a9a164850a9d73716))
* 精简依赖 ([826b8b3](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/826b8b35c3668287e34a0b7eae59f1a13b403425))
* 升级依赖 ([59794a1](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/59794a1106af05ef6e77a9de5d54fd74a1b84a6a))
* 升级依赖 ([1f3a87b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/1f3a87b288c953496446b79646a2d4fce1c6c82a))
* 升级依赖 ([981e74b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/981e74b576110dc60b04f257cbe9a73e99c0bfb3))
* 升级依赖 ([9c1c0b2](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/9c1c0b2df8a39544de64a75aaab5ccfc89139517))
* 修改自动格式化配置 ([8321264](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/8321264779e35f262f360e8a9cfbed9132b34190))
* 修改vite配置 ([1578b3a](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/1578b3a2cd1831f89bc1739b66ec6e9ac7bd4063))
* 依赖更新 ([cbeafbe](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/cbeafbe9b23a7e861c43007dfd4e6b2e0069aff5))
* 依赖升级 ([4472c1c](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/4472c1cb7e612ce6ed9f79b67742c373f664806a))
* 依赖升级 ([9e98990](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/9e9899083a741c2bab46eef0b5edcaa07572f7f8))
* 依赖升级 ([b5e01da](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/b5e01da4adea4bd94c89edf61262e44adf2ecbae))
* 依赖升级 ([aeac393](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/aeac393ac1b5b13a5b2c4b8d943ba27df5735a62))
* 依赖升级 ([8ac6d03](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/8ac6d038b2c7dfe98a61a927f1d323cc26990171))
* 依赖升级 ([df8dd27](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/df8dd27ef1d7f94438c56250ee1982e5c2186c80))
* 优化构建配置 ([106ff13](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/106ff1366c87c73e34a1839f33f2a4a1eba465cf))


### 🚧其他

* 🎨 样式配置优化 ([cea913b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/cea913b1f9648341a103e6b99141dd58fa285a63))
* 安全问题处理 ([e403ccb](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/e403ccbd94375efeb9f4a26dd7651cdc00326bf9))
* 案例更新 ([9ca0f8b](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/9ca0f8b62a92f2fe974523c8aa955c9477d5faa1))
* 代码调整 ([d0b1081](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/d0b1081ef4120516c793bdf69ff74958455045a0))
* 调整样式重置 ([7e70a75](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/7e70a75f1032c2f6d2b3b76ab8e6a44e281adeaf))
* 调整unocss默认单位为px ([b5c64b9](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/b5c64b9100eda67e03c2c2788699d4ca44b0c4ca))
* 更新依赖 ([390af43](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/390af43ca9aca9b47f2dc6a1ceff8f2e0d896187))
* 更新依赖库 ([ffa7a23](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/ffa7a23ad52c9faad1b6b9428d5ddbe699c66dfb))
* 环境变量修改 ([69ea54f](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/69ea54f7ccc002d51279ae13b9d1a18cd04a9c7c))
* 默认开启调试工具 ([0496a80](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/0496a80d8b1146615709459bfd1a29c705fd92a8))
* 清理插件 ([14214ac](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/14214ac66e2d6f8d1cb0c705262b93ff24d886ce))
* 去掉默认配置@unocss/preset-rem-to-px ([7f1c65f](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/7f1c65fe434995dc2bb71aedde29ad8a393d2d23))
* 修复lint错误 ([df92209](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/df92209b6affbfa12f2cf78d8ebc4df3a23826ca))
* 修改更新日志生成规则 ([be0db12](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/be0db12bc27025d670e84c39f87efb9485297f1f))
* 修改配置 ([f33af87](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/f33af878aba12dbcdf967558136dcc76e80fe4e6))
* 修改字体 ([686225e](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/686225e9354e5bfdef53b7a8a383748771a91865))
* 一些小调整 ([0a36b71](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/0a36b71051152add2e9d6469d36ec02b956c53fd))
* 优化类型错误 ([2db1d3f](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/2db1d3fefe3607b21ad63f9e2916715dfe7b1338))
* 优化配置 ([2f27eb6](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/2f27eb627310346d944b98d6fac61d2479bceaf6))
* 优化lint配置 ([9e404a4](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/9e404a42e12c6960578dfcc5f0e6f4caa16235f5))
* 增加发版自动build ([ac1d5b1](http://10.18.101.198:30088/zdww/template-vue3-pc/commit/ac1d5b13eab825fdd94f650da4d966402ed2340d))

# 更新日志

### [0.1.3](https://gitee.com/misthin/vue3-antdv/compare/v0.1.2...v0.1.3) (2023-05-26)

### [0.1.2](https://gitee.com/misthin/vue3-antdv/compare/v0.1.1...v0.1.2) (2023-05-26)


### 🚧其他

* **release:** v0.0.2 ([e007953](https://gitee.com/misthin/vue3-antdv/commit/e007953041e4df0d115be887733a4a3df13ee3dc))
* **release:** v0.0.3 ([7ce94ec](https://gitee.com/misthin/vue3-antdv/commit/7ce94ec0b15a01ec5b4921705d0b95668e9aa2b2))
* **release:** v0.0.4 ([238fda0](https://gitee.com/misthin/vue3-antdv/commit/238fda08a967cbde5ec2d6c2b14cd892f8b9fc16))
* **release:** v0.1.2 ([4a64d65](https://gitee.com/misthin/vue3-antdv/commit/4a64d6559f1a252af6d7edff3e24df74f2252f53))
* **release:** v0.1.3 ([e106dde](https://gitee.com/misthin/vue3-antdv/commit/e106dde9b155598e12212c388643f47e19270987))


### 🐛Bug修复

* 🐛 修复版本号错误 ([e07551d](https://gitee.com/misthin/vue3-antdv/commit/e07551ded39aacf595c52cbddc7acf03f502cf40))
* 优化配置 ([f3ab55a](https://gitee.com/misthin/vue3-antdv/commit/f3ab55a6064e21ac05cab0cc26cc79d8fc1a3188))


## v0.0.1


### 🚀 Enhancements

  - 功能完善 (5344dff)
  - 增加自定义图标相关 (d66259d)
  - 多分辨率适配 (ce7f359)
  - 增加设置title标题 (38a9a2d)
  - 菜单支持多级、移除菜单展开对 route.matched 的依赖、更改 activeMenu 的逻辑 (70d39ba)
  - 增加两套模板 (d3a432a)
  - 忽略 components 目录类型声明，步骤一 (28f3777)
  - 忽略 components 目录类型声明，步骤二 (e19cc09)
  - 忽略 components 目录类型声明，步骤三 (a84c373)
  - Lint-staged 移除 [git add]，更改为直接使用 eslint 驱动 + stylelint 检测修复 (b0380fd)
  - 增加发布脚本 (c411129)

### 🔥 Performance

  - 对等依赖缺失和错误处理+类型错误处理 (49a556f)

### 🩹 Fixes

  - 优化全局组件注册方式 (e4d41e3)
  - 代码优化 (92905df)
  - 修复lint警告 (f167a59)
  - 优化request代码 (d7e261b)
  - 优化代码 (160cc34)
  - 优化菜单查找算法 (a0727e9)
  - 升级依赖 (ae77988)
  - 优化代码 (ab1869e)
  - 修复footer组件不是模块问题 (fa0d5d6)
  - 暂时修复ref打包后报错问题 (ce43424)
  - 升级依赖 (1efd0e8)
  - 优化mock配置 (cfbfe15)
  - 修复报错 (1c1c62e)
  - 修复动态路由切换很慢的问题 (92b15d9)
  - 增加对tsx组件支持 (60b0053)
  - 修复loading动画无效问题 (24cbf83)
  - 菜单树滚动高度修正 (1d83ec0)
  - 代码优化 (4fd2cf9)
  - 修复面包屑异常的 bug (e417846)
  - 移除错误的属性 (ce3a482)
  - 修正 server.proxy 配置中的 rewrite 逻辑 (8e74b69)
  - 修复打包样式丢失问题 (f5f524c)
  - 修复pinia报错问题 (072c3ef)
  - 修复表格分页变化失效问题 (0c0bbad)
  - 修复配置管理表格组件无法编辑问题 (2e62ca5)
  - 依赖升级 (ca41f86)
  - Bug修复 (dbb92ec)
  - 修改代理配置 (9dcb98b)
  - 优化代码 1. 调整结构 2. 重构请求封装 (8396635)
  - 优化插件 1. storage增加加密 2. 优化请求 (55c7436)
  - 增加文件上传下载demo (83187e7)
  - 优化上传进度 (b03595d)
  - 优化 (c3ad2da)
  - 代码优化 (b54e439)
  - 代码优化 (7e2b04e)
  - 修改拦截器bug (a51054e)
  - 命名优化 (5011e52)
  - 解决打包后modal失效问题，需要手动引入 (bd39bef)
  - 修复代码坏味道 (46c059d)
  - 优化版本信息插入方式 (0c9d320)
  - **build:** 🔧 调整构建插件 (a40d30d)
  - Request增加重复请求取消时间 (38ed208)
  - 修复单次错误 (a567f29)

### 💅 Refactors

  - 优化结构和配置 (43d040c)
  - 🎨 优化配置 (2120b75)
  - 组件优化 (3fa4b88)

### 📖 Documentation

  - 文档优化 (7a6ecca)
  - 文档修改 (5cf2627)

### 📦 Build

  - 修改cdn (8fc1e02)
  - 优化构建配置 (cb06adc)
  - 升级依赖 (1726625)
  - 构建环境升级 (1a5093d)
  - 更改构建配置 (ba84c40)
  - 修改eslint规则 (1c48e52)
  - 升级构建工具 (d2748f1)
  - 开启css sourceMap (053fdda)
  - Vue3响应式语法糖支持 (55fe23b)
  - 增加打包分析插件 (b1ecdb6)
  - 依赖版本升级 (76ed255)
  - 修改lint规则及增加单元测试 (79bb5bd)
  - 🚧 升级依赖 (339ecf0)
  - 构建优化 (8b0ff1c)

### 🏡 Chore

  - 框架优化 (2461ccf)
  - 项目结构调整 (648da56)
  - **release:** 0.1.1 (7bdb398)
  - 功能优化 (926c7da)
  - 优化代码 (faa3952)
  - 代码优化 (1735b87)
  - 升级依赖 (53e7ac6)
  - 优化类型声明 (3def5c1)
  - **release:** 0.1.2 (948312a)
  - 升级依赖 (163f00b)
  - 升级依赖 (ddad444)
  - 升级依赖 (b98ca71)
  - 部分代码改成setup-script (023042b)
  - 修改setup-script (719ef82)
  - 升级依赖 (2296ce6)
  - 升级依赖 (7c978ce)
  - 增加store类型推断 (3f72f59)
  - 升级 (74d6bba)
  - 升级依赖包及代码优化 (f5a9392)
  - 升级依赖 (efe87de)
  - 升级依赖 (9d4b625)
  - 升级依赖 (f2019d5)
  - 依赖升级 (8ab8b65)
  - 增加vite支持 (f1eea7b)
  - 清理代码 (c4c5a67)
  - 更新技术栈 (bb6e979)
  - 优化 (de55b97)
  - 优化代码 (fd23579)
  - 升级依赖 (125f001)
  - 升级依赖 (c6cf7bd)
  - 组件更新 (44ed5f1)
  - 升级依赖 (d06ae44)
  - 依赖升级 (754a8bb)
  - 移除包工具限制 (1f6aef0)
  - 升级依赖 (510f6c4)
  - 升级依赖 (9b04cce)
  - 文件清理 (5f28c91)
  - 优化配置文件 (c781f1b)
  - 对于文件清理 (7b9449f)
  - 升级依赖 (b541836)
  - 优化构建配置 (fc17810)
  - 升级依赖 (97641f0)
  - 升级依赖 (5a6ed91)
  - 升级依赖 (f48edb7)
  - 升级依赖 (a86eb55)
  - 增加团队代码规范 (6c398fd)
  - 升级依赖 (84ea281)
  - 升级依赖 (1b54809)
  - 升级依赖 (e65177f)
  - 构建工具配置调整 (3f4af73)
  - 升级依赖 (0e72672)
  - 优化代码规则 (01da1e1)
  - Release v0.0.1 (5149c3d)
  - **release:** V0.0.2 (6d02048)

### 🎨 Styles

  - 修改样式 (95f85a5)
  - 部分 ts 错误修复 (950218a)
  - Eslint样式修复 (ffe1a7a)

### ❤️  Contributors

- Wangwenbing ([@BWrong](http://github.com/BWrong))
- Wangshuhao <<EMAIL>>
- Bwrong ([@BWrong](http://github.com/BWrong))
