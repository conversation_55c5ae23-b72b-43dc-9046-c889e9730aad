// @import '@unocss/reset/tailwind-compat.css';
@import 'ant-design-vue/dist/reset.css';

html,
body {
  padding: 0;
  margin: 0;
}
.slide-leave-active,
.slide-enter-active {
  transition: all 0.3s ease;
}
.slide-enter-from {
  opacity: 0;
  transform: translateX(-10px);
}
.slide-leave-to {
  opacity: 0;
  transform: translateX(10px);
}

.clear-float:before,
.clear-float:after {
  clear: both;
  display: table;
  width: 0;
  height: 0;
  content: ' ';
}
::view-transition-new(root),
::view-transition-old(root) {
  /* 关闭默认动画，否则影响自定义动画的执行 */
  animation: none;
}
.dark::view-transition-old(root) {
  z-index: 9999;
}
