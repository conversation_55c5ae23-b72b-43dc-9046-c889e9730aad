server {
  listen 80;
  server_name localhost;
  root /usr/share/nginx/html;
  index index.html index.htm;
  access_log /var/log/nginx/host.access.log main;
  error_log /var/log/nginx/error.log error;
  location / {
    try_files $uri $uri/ /index.html;
    proxy_set_header Host $host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
  }
  # 接口代理
  location ~ ^/api {
    # add_header Access-Control-Allow-Origin *;
    # add_header Access-Control-Allow-Methods 'GET,PUT,DELETE,POST,OPTIONS';
    # add_header Access-Control-Allow-Headers 'DNT,X-Mx-ReqToken,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type,Authorization';
    # 地址换成自己的接口地址
    proxy_pass http://***********:8080;
    proxy_set_header X-Real_IP $remote_addr;
    proxy_set_header Host $host;
    proxy_set_header X_Forward_For $proxy_add_x_forwarded_for;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection 'upgrade';
  }
  error_page 404 /404.html;

  # redirect server error pages to the static page /50x.html
  #
  error_page 500 502 503 504 /50x.html;
  location = /50x.html {
    root /usr/share/nginx/html;
  }
  #开启gzip，提高访问速度
  gzip on;
  gzip_min_length 5k;
  gzip_buffers 4 16k;
  gzip_comp_level 2;
  gzip_types text/plain application/javascript application/x-javascript text/css application/xml text/javascript image/jpeg image/gif image/png;
  gzip_vary off;
  gzip_disable "MSIE [1-6]\.";
}
